package com.pass.hbl.manager.backend.persistence.service.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.LeagueStatisticsDetailDO;
import com.pass.hbl.manager.backend.persistence.dto.hm.LeagueStatisticsResponseDto;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmClientRequest;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.exception.RateLimitExceededException;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmClientRequestRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmLeagueRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.ParameterService;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.RateLimitingHandler;
import com.pass.hbl.manager.backend.persistence.util.ParameterDefaults;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.pass.hbl.manager.backend.persistence.util.Constants.SYSTEM_USERNAME;
import static java.util.Objects.isNull;

@Service
@Slf4j
public class LeagueStatisticsService {

    private final HmLeagueRepository leagueRepository;
    private final ParameterService parameterService;
    private final HmClientRequestRepository clientRequestRepository;
    private final SeasonService seasonService;
    private final RateLimitingHandler rateLimitHandler;

    @Getter
    private int maxStatPageSize = ParameterDefaults.DEFAULT_MAX_LEAGUE_STAT_PAGE_SIZE;
    @Getter
    private int rateLimitMinutes = ParameterDefaults.DEFAULT_PARAM_LEAGUE_STAT_RATE_LIMIT_MINUTES;
    @Getter
    private boolean rateLimitingActive = ParameterDefaults.DEFAULT_PARAM_LEAGUE_STAT_RATE_LIMITING_ACTIVE;
    @Getter
    private int forceRefreshCacheAfterMinutes = ParameterDefaults.DEFAULT_PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES;

    /**
     * Cache for league IDs to support pagination = Triple <left,Middle,right> represents the league ids who have
     * performed any change (related to the requested data structure e.g. joined leagues) in this time frame.
     * right -> IDs
     * middle -> changeStart e.g. 12:00 (requested date)
     * left -> changeEnd e.g 12h30 (cache update date, client request sent at)
     */
    private Triple<LocalDateTime, LocalDateTime, List<UUID>> cacheData = Triple.of(null, null, new ArrayList<>());
    private int cacheLeagueCount = 0;

    public LeagueStatisticsService(HmLeagueRepository leagueRepository,
                                 ParameterService parameterService,
                                 HmClientRequestRepository clientRequestRepository,
                                 SeasonService seasonService,
                                 RateLimitingHandler rateLimitHandler) {
        this.leagueRepository = leagueRepository;
        this.parameterService = parameterService;
        this.clientRequestRepository = clientRequestRepository;
        this.seasonService = seasonService;
        this.rateLimitHandler = rateLimitHandler;
    }

    @Transactional(readOnly = true)
    public List<LeagueStatisticsResponseDto> getAllLeagueStatistics(LocalDateTime changedAfter, Pageable pageable, String requestUrl, String externalClient) throws InvalidOperationException, FormatException, RateLimitExceededException, EntityNotExistException {

        log.info("getAllLeagueStatistics: changedAfter: {}, pageable: {}, client: {}", changedAfter, pageable, externalClient);

        boolean isRateLimitingActive = parameterService.getAsBoolean( ParameterDefaults.PARAM_LEAGUE_STAT_RATE_LIMITING_ACTIVE, ParameterDefaults.DEFAULT_PARAM_LEAGUE_STAT_RATE_LIMITING_ACTIVE, SYSTEM_USERNAME);

        Optional<HmClientRequest> existingRequestOpt = clientRequestRepository.findByRequestAndExternalClient(requestUrl, externalClient);

        if (isRateLimitingActive) {
            rateLimitHandler.checkRateLimit(changedAfter, pageable, requestUrl, externalClient, existingRequestOpt, rateLimitMinutes);
        }

        maxStatPageSize = parameterService.getAsInteger(ParameterDefaults.PARAM_MAX_LEAGUE_STAT_PAGE_SIZE, ParameterDefaults.DEFAULT_MAX_LEAGUE_STAT_PAGE_SIZE, SYSTEM_USERNAME);

        rateLimitMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_LEAGUE_STAT_RATE_LIMIT_MINUTES, ParameterDefaults.DEFAULT_PARAM_LEAGUE_STAT_RATE_LIMIT_MINUTES, SYSTEM_USERNAME);

        forceRefreshCacheAfterMinutes = parameterService.getAsInteger(ParameterDefaults.PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES, ParameterDefaults.DEFAULT_PARAM_FORCE_REFRESH_CACHE_AFTER_MINUTES, SYSTEM_USERNAME);

        LocalDateTime now = LocalDateTime.now();
        boolean forceRefreshCache = !isNull(cacheData.getLeft()) && now.isAfter(cacheData.getLeft().plusMinutes(forceRefreshCacheAfterMinutes));

        // Check if we need to refresh the cache based on multiple conditions:
        // 1. Cache hasn't been initialized yet (cacheData.getLeft() == null) -> league Ids list is empty
        // 2. Filter parameter changed (cacheData.getMiddle() != changedAfter) -> changeStart-date was changed
        // 3. forceRefreshCache if "forceRefreshCacheAfterMinutes" minutes passed -> Cache expiration time (in minutes)
        boolean refreshCache = cacheData.getLeft() == null || cacheData.getMiddle() == null || !Objects.equals(cacheData.getMiddle(), changedAfter) || forceRefreshCache;

        if (refreshCache) {
            log.info("Refreshing league statistics cache with changedAfter: {}", changedAfter);

            UUID currentSeasonId = seasonService.getCurrentSeason().getId();
            List<UUID> leagueIds;
            if (isNull(changedAfter)) {
                leagueIds = leagueRepository.findAllIdsBySeasonId(currentSeasonId);
            } else {
                leagueIds = leagueRepository.findBySeasonIdAndChangedAfter(changedAfter,currentSeasonId);
            }
            cacheData = Triple.of(LocalDateTime.now(), changedAfter, leagueIds);
            cacheLeagueCount = leagueRepository.countBySeasonId(currentSeasonId);

            log.info("Found {} leagues in current season modified after: {}", leagueIds.size(), changedAfter);
        }

        // If pageable size is > maxStatPageSize, use maxStatPageSize instead
        int pageSize = pageable.getPageSize() > maxStatPageSize ? maxStatPageSize : pageable.getPageSize();
        int pageNumber = pageable.getPageNumber();

        // Calculate pagination
        int startIndex = pageNumber * pageSize;
        int endIndex = Math.min(startIndex + pageSize, cacheData.getRight().size());

        // Check if the requested page is valid
        if (startIndex >= cacheData.getRight().size()) {
            return Collections.emptyList(); // Return empty list if page is out of bounds
        }

        List<UUID> pageLeagueIds = cacheData.getRight().subList(startIndex, endIndex);

        List<LeagueStatisticsDetailDO> leagueDetails = leagueRepository.findDetailByIdIn(pageLeagueIds);

        return leagueDetails.stream()
                .map(detail -> new LeagueStatisticsResponseDto(
                        detail.getId().toString(),
                        detail.getName(),
                        detail.getTotalMemberCount(),
                        cacheLeagueCount
                ))
                .collect(Collectors.toList());
    }
}
