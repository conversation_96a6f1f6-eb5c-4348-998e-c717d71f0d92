package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.HmLeagueInfoDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.HmLeagueOwnerDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.LeagueBasicInfoDO;
import com.pass.hbl.manager.backend.persistence.domain.hm.LeagueStatisticsDetailDO;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmLeague;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

public interface HmLeagueRepository extends PagingAndSortingRepository<HmLeague, UUID> {

    @Query("select l.name from HmLeague l where l.id = :id and l.deleted = false")
    String findLeagueNameById(@Param("id") UUID id);

    @Query("select l.owner.id from HmLeague l where l.id = :id and l.deleted = false")
    UUID findLeagueOwnerIdById(@Param("id") UUID id);

    @Query("select l.id as id, l.name as name, l.owner.id as ownerId from HmLeague l where l.id in :leagueIds and l.deleted = false")
    List<HmLeagueOwnerDO> findAllLeagueOwnerInfo(@Param("leagueIds") List<UUID> leagueIds);

    @Modifying
    @Query("update HmLeague l set l.previousLeague.id = null, l.modifiedAt = CURRENT_TIMESTAMP where l.previousLeague.id = :previousLeagueId and l.deleted = false")
    void clearPreviousLeague(@Param("previousLeagueId") UUID previousLeagueId);

    List<HmLeague> findAllByIdIn(List<UUID> ids);

    List<HmLeague> findAllBySeasonId(UUID seasonId);

    List<HmLeague> findAllBySeasonIdAndCreatedAtLessThan(UUID seasonId, LocalDateTime referenceDate);

    @Modifying
    @Query("update HmLeague l set l.owner.id = :ownerId, l.modifiedAt = CURRENT_TIMESTAMP where l.id = :leagueId and deleted = false")
    int updateOwner(@Param("leagueId") UUID leagueId, @Param("ownerId") UUID ownerId);

    @Query("select l.id as id, l.name as name, l.description as description, l.owner.id as ownerId, l.picture as imageId, l.publicAccess as publicAccess, l.previousLeague.id as previousLeagueId from HmLeague l where l.season.id = :seasonId and l.deleted = false")
    List<HmLeagueInfoDO> findAllLeagueInfoDOsBySeasonId(@Param("seasonId") UUID seasonId);

    @Query("select l.previousLeague.id from HmLeague l where (not l.previousLeague is null) and l.previousLeague.season.id = :previousSeasonId and l.deleted = false")
    List<UUID> findAllLeaguesAlreadyReset(@Param("previousSeasonId") UUID previousSeasonId);

    @Modifying
    @Query("update HmLeague l set l.deleted = true, l.deletedAt = CURRENT_TIMESTAMP where (l.id in :ids and l.deleted = false)")
    int deleteByIdIn(@Param("ids") List<UUID> ids);

    @Query("select l.previousLeague.id from HmLeague l where l.id = :id and l.deleted = false")
    UUID findPreviousLeagueById(@Param("id") UUID id);

    @Modifying
    @Query("update HmLeague l set l.isActive = :activeState, l.modifiedAt = CURRENT_TIMESTAMP, l.inactiveSince = :inactiveSince where l.id = :leagueId and l.deleted = false and not (l.isActive = :activeState)")
    int setLeagueActiveState(UUID leagueId, boolean activeState, LocalDateTime inactiveSince);

    @Query("select l.isActive from HmLeague l where l.id = :leagueId and l.deleted = false")
    boolean isLeagueActive(UUID leagueId);
    /* =================================================================================================================
     * league search queries
     * ============================================================================================================== */

    Page<HmLeague> findAllBySeasonId(UUID seasonId, Pageable pageable);

    @Query("select l.name from HmLeague l where l.season.id = :seasonId and l.deleted = false and LOWER(l.name) LIKE LOWER(CONCAT('%', :name, '%'))")
    List<String> findAllLeagueNameBySeasonId(@Param("seasonId") UUID seasonId, Pageable pageable, String name);

    @Query("select l.name from HmLeague l where l.season.id = :seasonId and l.deleted = false")
    List<String> findAllLeagueNameBySeasonId(@Param("seasonId") UUID seasonId, Pageable pageable);

    Page<HmLeague> findBySeasonIdAndNameIgnoreCaseLike(UUID seasonId, String name, Pageable pageable);

    Page<HmLeague> findBySeasonIdAndPublicAccess(UUID seasonId, boolean publicAccess, Pageable pageable);

    @Query(value = "SELECT l FROM HmLeague l where l.maxSize > size(l.leagueMemberships) and l.season.id = :seasonId")
    Page<HmLeague> findByFree(UUID seasonId, Pageable pageable);

    @Query(value = "SELECT l FROM HmLeague l where l.maxSize <= size(l.leagueMemberships) and l.season.id = :seasonId")
    Page<HmLeague> findByClosed(UUID seasonId, Pageable pageable);

    Page<HmLeague> findBySeasonIdAndNameIgnoreCaseLikeAndPublicAccess(UUID seasonId, String name, boolean publicAccess, Pageable pageable);

    @Query(
            value = "SELECT l.* FROM hm.league l " +
                    "LEFT JOIN (SELECT lm.league_id, COUNT(lm.id) AS membership_count " +
                    "           FROM hm.league_membership lm GROUP BY lm.league_id) lm_count " +
                    "ON lm_count.league_id = l.id " +
                    "WHERE l.season_id = :seasonId " +
                    "AND l.deleted = false " +
                    "AND l.public_access = :publicAccess " +
                    "AND l.max_size > COALESCE(lm_count.membership_count, 0)",
            countQuery = "SELECT COUNT(*) FROM hm.league l " +
                    "LEFT JOIN (SELECT lm.league_id, COUNT(lm.id) AS membership_count " +
                    "           FROM hm.league_membership lm GROUP BY lm.league_id) lm_count " +
                    "ON lm_count.league_id = l.id " +
                    "WHERE l.season_id = :seasonId " +
                    "AND l.deleted = false " +
                    "AND l.public_access = :publicAccess " +
                    "AND l.max_size > COALESCE(lm_count.membership_count, 0)", nativeQuery = true)
    Page<HmLeague> findByPublicAccessAndFree(@Param("seasonId") UUID seasonId, @Param("publicAccess") boolean publicAccess, Pageable pageable);

    @Query(value = "SELECT l FROM HmLeague l where l.season.id = :seasonId and l.publicAccess = :publicAccess and l.maxSize <= size(l.leagueMemberships)")
    Page<HmLeague> findByPublicAccessAndClosed(@Param("seasonId") UUID seasonId, @Param("publicAccess") boolean publicAccess, Pageable pageable);

    @Query(value = "SELECT l FROM HmLeague l where l.season.id = :seasonId and lower(l.name) like lower(:name) and l.maxSize > size(l.leagueMemberships)")
    Page<HmLeague> findByNameIgnoreCaseLikeAndFree(@Param("seasonId") UUID seasonId, @Param("name") String name, Pageable pageable);

    @Query(value = "SELECT l FROM HmLeague l where l.season.id = :seasonId and lower(l.name) like lower(:name) and l.maxSize <= size(l.leagueMemberships)")
    Page<HmLeague> findByNameIgnoreCaseLikeAndClosed(@Param("seasonId") UUID seasonId, @Param("name") String name, Pageable pageable);

    @Query(value = "SELECT l FROM HmLeague l where l.season.id = :seasonId and lower(l.name) like lower(:name) and l.publicAccess = :publicAccess and l.maxSize > size(l.leagueMemberships)")
    Page<HmLeague> findByNameIgnoreCaseLikeAndPublicAccessAndFree(@Param("seasonId") UUID seasonId, @Param("name") String name, @Param("publicAccess") boolean publicAccess, Pageable pageable);

    @Query(value = "SELECT l FROM HmLeague l where l.season.id = :seasonId and lower(l.name) like lower(:name) and l.publicAccess = :publicAccess and l.maxSize <= size(l.leagueMemberships)")
    Page<HmLeague> findByNameIgnoreCaseLikeAndPublicAccessAndClosed(@Param("seasonId") UUID seasonId, @Param("name") String name, @Param("publicAccess") boolean publicAccess, Pageable pageable);

    List<HmLeague> findBySeasonIdAndNameIgnoreCase(UUID seasonId, String name);

    @Query("""
           SELECT l.id as id, l.name as name
           FROM HmLeague l
           WHERE l.season.id = :seasonId
           AND l.owner.id = :ownerId
           AND l.deleted = false
           """)
    List<LeagueBasicInfoDO> findBasicInfoBySeasonIdAndOwnerId(@Param("seasonId") UUID seasonId, @Param("ownerId") UUID ownerId);

    @Query("""
           SELECT l.id
           FROM HmLeague l
           WHERE l.season.id = :seasonId
           AND ( (l.modifiedAt > :changedAfter AND l.deleted = false )
              OR (l.createdAt > :changedAfter AND l.deleted = false)
              OR l.deletedAt > :changedAfter)
           """)
    List<UUID> findBySeasonIdAndChangedAfter(LocalDateTime changedAfter, UUID seasonId);

    @Query("""
        SELECT l.id as id FROM HmLeague l
        LEFT JOIN HmLeagueMembership m ON l = m.league
        WHERE l.season.id = :seasonId  and l.deleted = false
        """)
    List<UUID> findAllIdsBySeasonId(@Param("seasonId") UUID seasonId);

    @Query("""
           SELECT l.id as id, l.name as name, (SELECT COUNT(m.id) FROM HmLeagueMembership m WHERE m.league.id = l.id AND m.deleted = false) as totalMemberCount
           FROM HmLeague l
           WHERE l.id IN :ids AND l.deleted = false
           """)
    List<LeagueStatisticsDetailDO> findDetailByIdIn(@Param("ids") List<UUID> ids);

    int countBySeasonId(UUID seasonId);
}
